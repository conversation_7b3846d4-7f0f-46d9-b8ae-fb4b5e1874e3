%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 0}
  m_Name: Chort_Run
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves:
  - curve:
    - time: 0
      value: {fileID: 21300000, guid: ba5e738f47aed3e4087c58d87037f777, type: 3}
    - time: 0.06666667
      value: {fileID: 21300000, guid: 93650d29aaac8a640a9420b5dc767752, type: 3}
    - time: 0.13333334
      value: {fileID: 21300000, guid: e7e5a10212fcdca42a19c72bdd674448, type: 3}
    - time: 0.2
      value: {fileID: 21300000, guid: ad5bda7bc42ceb14fab6d5324a40c20a, type: 3}
    attribute: m_Sprite
    path: 
    classID: 212
    script: {fileID: 0}
  m_SampleRate: 15
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 0
      script: {fileID: 0}
      typeID: 212
      customType: 23
      isPPtrCurve: 1
    pptrCurveMapping:
    - {fileID: 21300000, guid: ba5e738f47aed3e4087c58d87037f777, type: 3}
    - {fileID: 21300000, guid: 93650d29aaac8a640a9420b5dc767752, type: 3}
    - {fileID: 21300000, guid: e7e5a10212fcdca42a19c72bdd674448, type: 3}
    - {fileID: 21300000, guid: ad5bda7bc42ceb14fab6d5324a40c20a, type: 3}
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.26666668
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves: []
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_GenerateMotionCurves: 0
  m_Events: []
